# ==============================================================================
# AI内容半自动化填充助手 V1.2 - 终极对接版
# 
# ** 全新的工作流程 **
#
# 1. 打开一个【新】终端窗口，运行一个特殊的命令来启动 "AutomationBot" 浏览器。
#    (这个命令我会单独教您)
# 2. 让那个终端窗口保持打开状态。
# 3. 打开【另一个】终端窗口，进入项目文件夹，运行本脚本。
# 4. 脚本会自动连接到已打开的浏览器，并完成所有操作。
#
# ==============================================================================

# 导入我们需要的工具包
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

# --- 配置文件 ---
# 这是我们要连接的“自动化对接口”的门牌号
DEBUGGER_PORT = 9222

NOTE_FILE_PATH = "my_note.txt"
TARGET_URL = "https://creator.xiaohongshu.com/publish/publish"

# --- 读取稿件内容 ---
print(f"正在从 {NOTE_FILE_PATH} 读取稿件...")
try:
    with open(NOTE_FILE_PATH, 'r', encoding='utf-8') as f:
        content = f.read()
    parts = content.split('---', 1)
    note_title = parts[0].strip()
    note_body = parts[1].strip()
    print("稿件读取成功！")
except Exception as e:
    print(f"读取稿件失败，请检查 {NOTE_FILE_PATH} 文件是否存在并且内容格式正确。错误: {e}")
    exit()

# --- 主程序开始 ---
print("自动化脚本启动...")
driver = None
try:
    # 步骤1: 设置Chrome选项，准备连接到已运行的浏览器
    print("正在配置Chrome选项...")
    options = Options()
    options.add_experimental_option("debuggerAddress", f"127.0.0.1:{DEBUGGER_PORT}")
    print(f"准备连接到位于 localhost:{DEBUGGER_PORT} 的浏览器...")

    # 步骤2: 连接到您已手动打开的浏览器窗口
    # 我们不再需要 webdriver-manager 来安装驱动，因为我们不创建新浏览器
    # 但保留 service 的写法更稳妥
    service = ChromeService() 
    driver = webdriver.Chrome(service=service, options=options)
    print("连接成功！已接管浏览器控制权。")

    # 步骤3: 在已打开的窗口上进行操作
    print(f"正在当前页面进行操作，或跳转至: {TARGET_URL}")
    if "xiaohongshu.com" not in driver.current_url:
        driver.get(TARGET_URL)
    print("页面已就绪！")
    
    time.sleep(3)

    # 步骤4: 定位并填充标题
    print("正在寻找标题输入框...")
    wait = WebDriverWait(driver, 60)
    wait.until(EC.frame_to_be_available_and_switch_to_it((By.ID, "publish-iframe")))
    print("已进入编辑区。")

    title_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "input.title-input")))
    print("找到标题输入框！")
    title_input.clear()
    title_input.send_keys(note_title)
    print("标题填充完毕！")

    # 步骤5: 定位并填充正文
    print("正在寻找正文输入框...")
    body_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.ProseMirror")))
    print("找到正文输入框！")
    body_input.clear()
    body_input.send_keys(note_body)
    print("正文填充完毕！")

    # 步骤6: 任务完成
    print("\n🎉 任务完成！浏览器将保持被接管状态。")
    print("您可以继续手动操作，或关闭脚本（按 Ctrl+C）。")
    while True:
        time.sleep(1)

except Exception as e:
    print(f"\n自动化过程中出现错误: {e}")
    print("请检查：")
    print(f"1. 您是否已在另一个终端窗口，用特殊命令启动了'AutomationBot'浏览器？")
    print(f"2. 那个启动浏览器的终端窗口是否仍然保持打开状态？")
    print("请截图此错误信息寻求帮助。")

finally:
    print("脚本结束。")
    pass
